<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import AllPlatformIdentitiesList from '$lib/components/customers/AllPlatformIdentitiesList.svelte';
	import ConversationView from '$lib/components/conversation/ConversationView.svelte';
	import CustomerInfoPanel from '$lib/components/customer/CustomerInfoPanel.svelte';
	import { customerDetailStore } from '$lib/stores/customerDetailStore';
	import { customerWebSocket } from '$lib/websocket/customerWebSocket';
	import type { Customer, CustomerPlatformIdentity } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	
	export let data;

	let customerId: number;
	let customer: Customer | null = null;
	let selectedPlatformId: number | null = null;
	let loading = true;
	let allPlatformIdentities: CustomerPlatformIdentity[] = [];
	
	$: customerId = parseInt($page.params.customerId);
	$: urlPlatformId = $page.url.searchParams.get('platformId');
	$: showAll = $page.url.searchParams.get('showAll') === 'true';
	
	onMount(async () => {
		// Use pre-loaded data if available
		if (data) {
			customer = data.customer;
			allPlatformIdentities = data.allPlatformIdentities || [];
			loading = false;
		}
		
		// Auto-select platform if provided in URL
		if (urlPlatformId) {
			selectedPlatformId = parseInt(urlPlatformId);
			customerDetailStore.selectPlatform(selectedPlatformId);
		}
		
		// Connect WebSocket
		customerWebSocket.connect(customerId);
		customerWebSocket.subscribeToCustomer(customerId);
	});
	
	onDestroy(() => {
		customerWebSocket.disconnect();
	});
	
	function handlePlatformSelect(event: CustomEvent<{ platform: CustomerPlatformIdentity }>) {
		const platform = event.detail.platform;
		selectedPlatformId = platform.id;
		customerId = platform.customer;
		customerDetailStore.selectPlatform(selectedPlatformId);
		
		// Update URL to reflect selected platform and customer
		// goto(`/chat_center/${customerId}?platformId=${selectedPlatformId}&showAll=true`, { replaceState: true });
		
		// Load customer data if it's a different customer
		// if (customerId !== customer?.customer_id) {
		// 	loadCustomerData(customerId);
		// }
	}
	
	async function loadCustomerData(customerIdToLoad: number) {
		try {
			loading = true;
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerIdToLoad}/`, {
				credentials: 'include'
			});
			if (response.ok) {
				const data = await response.json();
				customer = data;
				customerDetailStore.setCustomer(data);
			}
		} catch (error) {
			console.error('Error loading customer:', error);
		} finally {
			loading = false;
		}
	}
</script>

<div class="flex h-screen bg-gray-100">
	<!-- Left Panel: All Platform Identities List (same as main page) -->
	<div class="w-80 bg-white border-r border-gray-200 flex flex-col">
		<AllPlatformIdentitiesList
			platformIdentities={allPlatformIdentities}
			{selectedPlatformId}
			currentCustomerId={customerId}
			on:select={handlePlatformSelect}
		/>
	</div>
	
	<!-- Middle Panel: Conversation -->
	<div class="flex-1 flex flex-col bg-white">
		{#if selectedPlatformId && customer}
			<ConversationView 
				customerId={customer.customer_id}
				platformId={selectedPlatformId}
			/>
		{:else}
			<div class="flex-1 flex items-center justify-center text-gray-500">
				<div class="text-center">
					<svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
					</svg>
					<p class="text-lg">Select a platform to view conversation</p>
				</div>
			</div>
		{/if}
	</div>
	
	<!-- Right Panel: Customer Info -->
	<div class="w-96 bg-white border-l border-gray-200">
		{#if customer}
			<CustomerInfoPanel {customer} />
		{/if}
	</div>
</div>

<style>
	/* Custom scrollbar styles */
	:global(.custom-scrollbar) {
		scrollbar-width: thin;
		scrollbar-color: #e5e7eb #f9fafb;
	}
	
	:global(.custom-scrollbar::-webkit-scrollbar) {
		width: 6px;
	}
	
	:global(.custom-scrollbar::-webkit-scrollbar-track) {
		background: #f9fafb;
	}
	
	:global(.custom-scrollbar::-webkit-scrollbar-thumb) {
		background-color: #e5e7eb;
		border-radius: 3px;
	}
	
	:global(.custom-scrollbar::-webkit-scrollbar-thumb:hover) {
		background-color: #d1d5db;
	}
</style>