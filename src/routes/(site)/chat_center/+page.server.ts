// import { services } from "$src/lib/api/features";
// import { redirect } from '@sveltejs/kit';
// import type { PageServerLoad } from './$types';
// import { error } from '@sveltejs/kit';
// import { getBackendUrl } from '$src/lib/config';

// export const load: PageServerLoad = async ({ fetch, url, cookies }) => {
//     try {
//         // Check for access token in cookies
//         let access_token = cookies.get('access_token');
//         let refresh_token = cookies.get('refresh_token');
//         if (!access_token) {
//             throw error(401, 'No access token available');
//         }

//         // Get query parameters
//         const page = url.searchParams.get('page') || '1';
//         const search = url.searchParams.get('search') || '';
//         const platform = url.searchParams.get('platform') || '';
        
//         // Build API URL with filters
//         // const apiUrl = new URL('/api/customers/', url.origin);
//         const apiUrl = new URL(`${getBackendUrl()}/customer/api/customers/`, url.origin);
//         apiUrl.searchParams.set('page', page);
//         if (search) apiUrl.searchParams.set('search', search);
//         if (platform) apiUrl.searchParams.set('platform', platform);
        
//         const response = await fetch(apiUrl.toString(),
//             {
//                 method: 'GET',
//                 headers: {
//                     'Authorization': `Bearer ${access_token}`,
//                     'Content-Type': 'application/json'
//                 }
//             }
//         );
        
//         if (!response.ok) {
//             throw error(response.status, 'Failed to load customers');
//         }
        
//         const data = await response.json();
        
//         return {
//             customers: data.customers || [],
//             total: data.total || 0,
//             page: parseInt(page),
//             hasMore: data.has_more || false
//         };
//     } catch (err) {
//         console.error('Error loading customers:', err);
//         throw error(500, 'Failed to load customers');
//     }
// };

// // export const load: PageServerLoad = async ({ cookies }) => {
// //     let access_token = cookies.get('access_token');
// //     let refresh_token = cookies.get('refresh_token');

// //     if (!access_token) {
// //         return {
// //             customers: [],
// //             error: 'No access token available'
// //         };
// //     }

// //     for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
// //         try {
// //             const chatCenterCustomerList = await services.customers.chatCenterCustomerList(access_token)
// //             if (chatCenterCustomerList.res_status === 401) {
// //                 throw error(401, 'Invalid access token!!!');
// //             }

// //             // TODO - Delete this
// //             console.log(`CHAT CENTER CUSTOMER LIST`, chatCenterCustomerList.customers)

// //             return {
// //                 customer: chatCenterCustomerList.customers || [],
// //                 access_token: access_token
// //             };
            
// //         } catch (err) {
// //             const refreshResponse = await services.users.refreshToken(refresh_token);
// //             const login_token = refreshResponse.login_token;

// //             if (login_token.length === 0) {
// //                 cookies.set("isLogin", 'false', { path: '/' })
// //                 throw redirect(302, '/login');
// //             } else {
// //                 access_token = login_token.access;
// //                 refresh_token = login_token.refresh;

// //                 cookies.set("access_token", access_token, { path: '/' });
// //                 cookies.set("refresh_token", refresh_token, { path: '/' })
// //             }
// //         }
// //     }
// // };





















import { services } from "$src/lib/api/features";
import type { PageServerLoad, Actions } from "./$types";
import { getBackendUrl } from '$src/lib/config';
import { redirect, error, fail } from "@sveltejs/kit";

export const load: PageServerLoad = async ({ fetch, url, cookies }) => {
    try {
        // Check for access token in cookies
        let access_token = cookies.get('access_token');
        let refresh_token = cookies.get('refresh_token');
        if (!access_token) {
            throw error(401, 'No access token available');
        }

        // Get query parameters
        const page = url.searchParams.get('page') || '1';
        const search = url.searchParams.get('search') || '';
        const platform = url.searchParams.get('platform') || '';
        
        // Fetch all platform identities instead of customers
        const apiUrl = new URL(`${getBackendUrl()}/customer/api/platform-identities/`, url.origin);
        apiUrl.searchParams.set('page', page);
        if (search) apiUrl.searchParams.set('search', search);
        if (platform) apiUrl.searchParams.set('platform', platform);
        
        const response = await fetch(apiUrl.toString(),
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        if (!response.ok) {
            throw error(response.status, 'Failed to load platform identities');
        }
        
        const data = await response.json();
        
        // // TODO - Delete this
        // console.log('Platform identities data:', data);
        
        return {
            platformIdentities: data.results || [],
            total: data.count || 0,
            page: parseInt(page),
            hasMore: !!data.next,
            access_token: access_token
        };
    } catch (err) {
        console.error('Error loading platform identities:', err);
        throw error(500, 'Failed to load platform identities');
    }
};

export const actions: Actions = {
    upload_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();

        // console.log(formData)

        let access_token = cookies.get('access_token');

        const content = formData.get("content");
        const customerId = formData.get("customer_id");
        const bodyData = { content };

        try {
            
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Upload Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    update_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const content = formData.get("note");
        const customerId = encodeURIComponent(formData.get("customerId"));
        const customerNoteId = encodeURIComponent(formData.get("customerNoteId"));
        const bodyData = { content };

        try {
            
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/${customerNoteId}/`;

            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(bodyData)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Update Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },

    delete_note: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        let access_token = cookies.get('access_token');

        const customerId = encodeURIComponent(formData.get("customerId"));
        const deleteNoteId = encodeURIComponent(formData.get("deleteNoteId"));

        console.log(`customerId-monitoring - ${customerId}`);
        console.log(`deleteNoteId-monitoring - ${deleteNoteId}`);

        try {
            
            const url = `${getBackendUrl()}/customer/api/customers/${customerId}/notes/${deleteNoteId}/`;

            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({})); // Handle case where JSON parsing fails
                throw new Error(`HTTP error! Status: ${response.status}, Errors: ${JSON.stringify(errorData)}`);
            }

            console.log("Note Delete Success");

            const result = await response.json();
            return { success: true, data: result };
        } catch (error) {
            console.error('Upload error:', error);
            return fail(500, { error: 'Summary upload failed' });
        }
    },
}
