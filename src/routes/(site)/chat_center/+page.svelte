<!-- +page.svelte -->
<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import PlatformIdentityList from '$lib/components/chat/PlatformIdentityList.svelte';
    import ConversationView from '$lib/components/conversation/ConversationView.svelte';
    import CustomerInfoPanel from '$lib/components/customer/CustomerInfoPanel.svelte';
    import { platformWebSocket } from '$lib/websocket/platformWebSocket';
    import type { CustomerPlatformIdentity, Customer } from '$lib/types/customer';
    import { getBackendUrl } from '$src/lib/config';
    
    export let data;
    
    let selectedPlatformId: number | null = null;
    let selectedCustomer: Customer | null = null;
    let loading = false;
    
    // Initialize with data from server
    let platformIdentities: CustomerPlatformIdentity[] = data.platformIdentities || [];
    let total = data.total || 0;
    let currentPage = data.page || 1;
    let hasMore = data.hasMore || false;
    
    onMount(() => {
        // Connect WebSocket for real-time updates
        platformWebSocket.connect();
    });
    
    onDestroy(() => {
        platformWebSocket.disconnect();
    });
    
    async function handlePlatformSelect(event: CustomEvent<{ platformId: number, customerId: number }>) {
        const { platformId, customerId } = event.detail;
        
        // Validate that we have both values
        if (!platformId || !customerId) {
            console.error('Missing platformId or customerId:', { platformId, customerId });
            return;
        }
        
        // Update selected platform ID - this will trigger ConversationView to reload
        selectedPlatformId = platformId;
        
        // Load customer details if not already loaded or if different customer
        if (!selectedCustomer || selectedCustomer.customer_id !== customerId) {
            await loadCustomerDetails(customerId);
        }
    }
    
    async function loadCustomerDetails(customerId: number) {
        try {
            loading = true;
            const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customerId}/`, {
                credentials: 'include'
            });
            
            if (response.ok) {
                selectedCustomer = await response.json();
            }
            // console.log(selectedCustomer)
        } catch (error) {
            console.error('Error loading customer:', error);
        } finally {
            loading = false;
        }
    }
    
    async function handleLoadMore(event: CustomEvent) {
        // Load more platform identities
        currentPage++;
        const response = await fetch(
            `${getBackendUrl()}/customer/api/platform-identities/?page=${currentPage}`,
            { credentials: 'include' }
        );
        
        if (response.ok) {
            const data = await response.json();
            platformIdentities = [...platformIdentities, ...data.results];
            hasMore = !!data.next;
        }
    }
</script>

<div class="flex h-screen bg-gray-100">
    <!-- Left Panel: All Platform Identities -->
    <div class="w-1/5 bg-white border-r border-gray-200 flex flex-col">
        <PlatformIdentityList 
            {platformIdentities}
            {selectedPlatformId}
            {hasMore}
            on:select={handlePlatformSelect}
            on:loadMore={handleLoadMore}
        />
    </div>
    
    <!-- Middle Panel: Conversation -->
    <div class="flex-1 flex flex-col bg-white">
        {#if selectedPlatformId && selectedCustomer}
            <ConversationView 
                customerId={selectedCustomer.customer_id}
                platformId={selectedPlatformId}
            />
        {:else}
            <div class="flex-1 flex items-center justify-center text-gray-500">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <p class="text-lg font-medium">Select a conversation</p>
                    <p class="text-sm text-gray-500 mt-1">Choose a platform identity from the list to view messages</p>
                </div>
            </div>
        {/if}
    </div>
    
    <!-- Right Panel: Customer Info -->
    <!-- <div class="w-84 bg-white border-l border-gray-200"> -->
    <div class="w-1/3 bg-white border-l border-gray-200 flex-shrink-0 overflow-hidden">
        {#if selectedCustomer}
            <CustomerInfoPanel 
                customer={selectedCustomer} 
                access_token={data.access_token}
            />
        {:else}
            <div class="p-6 text-center text-gray-500">
                <p>Select a conversation to view customer details</p>
            </div>
        {/if}
    </div>
</div>