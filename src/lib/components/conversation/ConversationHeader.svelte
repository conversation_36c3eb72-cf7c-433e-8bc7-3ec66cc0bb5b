<script lang="ts">
	export let customerName: string;
	export let channelName: string;
	export let connected: boolean = false;
	import { t } from '$lib/stores/i18n';
</script>

<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-lg font-semibold text-gray-900">{customerName}</h2>
			<p class="text-sm text-gray-500">{channelName}</p>
		</div>
		
		<div class="flex items-center space-x-4">
			<!-- Connection Status -->
			<div class="flex items-center space-x-2">
				<div class="w-2 h-2 rounded-full {connected ? 'bg-green-500' : 'bg-red-500'}"></div>
				<span class="text-sm text-gray-600">
					{connected ? t('connected') : t('disconnected')}
				</span>
			</div>
			
			<!-- Actions -->
			<button class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
				<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
			</button>
			
			<button class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
				<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
				</svg>
			</button>
		</div>
	</div>
</div>